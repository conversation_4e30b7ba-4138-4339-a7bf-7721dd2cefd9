<?php
namespace App\Http\Controllers;

use App\Models\ScheduleConfig;
use App\Models\ScheduleTimeSlot;
use App\Models\Tournament;
use Carbon\Carbon;
use Error;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ScheduleConfigController extends Controller
{

    private $timeSlotController;

    public function __construct()
    {
        $this->timeSlotController = new ScheduleTimeSlotController();
    }

    public function saveConfig($tournamentId, $locationId, $beginDate, $beginTime, $matchDuration, $breakDuration, $timeZone = null, $forceUpdate = false)
    {
        try {
            // Default timezone to UTC if not provided
            $timeZone = $timeZone ?? 'UTC';

            // Convert begin date/time to UTC for consistent storage
            $beginDateTimeLocal = Carbon::parse($beginDate . ' ' . $beginTime, $timeZone);
            $newBeginDateTimeUTC = $beginDateTimeLocal->utc();

            $newBeginDateUTC = $newBeginDateTimeUTC->format('Y-m-d');
            $newBeginTimeUTC = $newBeginDateTimeUTC->format('H:i:s');

            $currentConfig = ScheduleConfig::where('tournament_id', $tournamentId)
                ->where('location_id', $locationId)
                ->first();

            if ($currentConfig) {
                if ($newBeginDateTimeUTC < $currentConfig->begin_time || $forceUpdate) {
                    $currentConfig->update([
                        'begin_date' => $newBeginDateUTC,
                        'begin_time' => $newBeginTimeUTC,
                        'match_duration' => $matchDuration,
                        'break_match_duration' => $breakDuration,
                    ]);
                }
            } else {
                ScheduleConfig::create([
                    'tournament_id' => $tournamentId,
                    'location_id' => $locationId,
                    'begin_date' => $newBeginDateUTC,
                    'begin_time' => $newBeginTimeUTC,
                    'match_duration' => $matchDuration,
                    'break_match_duration' => $breakDuration,
                ]);
            }
        } catch (Exception $error) {
            Log::error('$error', [$error]);

            throw new Error($error);
        }
    }

    public function updateOrCreateConfig($tournamentId, $locationId, $beginDate, $newBeginTime, $endTimeOfDate, $matchDuration, $breakDuration, $timeZone = null, $forceUpdate = false, $configId = null)
    {

        $timeZone = $timeZone ?? 'UTC';

        // Convert begin date/time to UTC for consistent storage
        $beginDateTimeLocal = Carbon::parse($beginDate . ' ' . $newBeginTime, $timeZone);
        $newBeginDateTimeUTC = $beginDateTimeLocal->utc();

        if ($endTimeOfDate) {
            $endTimeOfDateLocal = Carbon::parse($beginDate . ' ' . $endTimeOfDate, $timeZone);
            $endTimeOfDateUTC = $endTimeOfDateLocal->utc();
        }

        $newBeginDateUTC = $newBeginDateTimeUTC->format('Y-m-d');
        $newBeginTimeUTC = $newBeginDateTimeUTC->format('H:i:s');

        try {
            if ($configId) {
                $currentConfig = ScheduleConfig::find($configId);
            } else {
                $currentConfig = ScheduleConfig::where('tournament_id', $tournamentId)
                    ->where('location_id', $locationId)
                    ->where('begin_date', $newBeginDateUTC)
                    ->first();
            }

            if ($currentConfig) {
                if ($newBeginDateTimeUTC < $currentConfig->begin_time || $forceUpdate) {
                    $currentConfig->update([
                        'begin_date' => $newBeginDateUTC,
                        'begin_time' => $newBeginTimeUTC,
                        'end_time' => $endTimeOfDate ? $endTimeOfDateUTC : null, // need to find way change
                        'match_duration' => $matchDuration,
                        'break_match_duration' => $breakDuration,
                    ]);
                }
            } else {
                ScheduleConfig::create([
                    'tournament_id' => $tournamentId,
                    'location_id' => $locationId,
                    'begin_date' => $newBeginDateUTC,
                    'begin_time' => $newBeginTimeUTC,
                    'end_time' => $endTimeOfDate ? $endTimeOfDateUTC : null,
                    'match_duration' => $matchDuration,
                    'break_match_duration' => $breakDuration,
                ]);
            }
        } catch (Exception $error) {
            Log::error('error in saveOrUpdateConfig', [$error]);
            throw new Error($error->getMessage());
        }

    }

    public function getScheduleConfigById($configId)
    {
        try {
            $config = ScheduleConfig::find($configId);

            // add time by header x-time-zone before response
            if ($config) {
                $timezone = request()->header('X-Time-Zone') ?? 'UTC';
                $beginTime = Carbon::parse($config->begin_date->format('Y-m-d') . ' ' . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone);
                $endTime = Carbon::parse($config->begin_date->format('Y-m-d') . ' ' . $config->end_time->format('H:i:s'), 'UTC')->setTimezone($timezone);
                $config->begin_date = $beginTime->copy()->format('Y-m-d H:i:s');
                $config->begin_time = $beginTime->copy()->format('H:i:s');
                $config->end_time = $endTime->copy()->format('H:i:s');

            }

            return response()->json([
                'success' => true,
                'data' => $config,
            ]);
        } catch (Exception $error) {
            Log::error('error in getScheduleConfigById', [$error]);

            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function findConflictConfig($tournamentId, $locationId, $beginDate, $timeZone, $currentConfigId)
    {

        $conflictId = null;

        $listConfigs = ScheduleConfig::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->where('id', '!=', $currentConfigId)
            ->get();

        $listConfigs->each(function ($config) use (&$conflictId, $beginDate, $timeZone) {
            $configBeginDate = (Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timeZone))->format('Y-m-d');

            Log::info('findConflictConfig config->begin_date', [$configBeginDate]);
            Log::info('$beginDate', [$beginDate]);

            if ($configBeginDate === $beginDate) {
                $conflictId = $config->id;
            };
        });

        return $conflictId;
    }

    public function updateScheduleConfig(Request $request)
    {
        try {
            $request->validate([
                'tournament_id' => 'required|integer',
                'location_id' => 'required|integer',
                'begin_date' => 'required|date',
                'begin_time' => ['required', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
                'end_time' => ['required', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
                'config_id' => 'required|integer',
                'match_duration' => 'required|integer|min:1',
                'break_duration' => 'required|integer|min:0',
                'time_slot_ids' => 'required|array',
                'time_slot_ids.*' => 'integer|exists:schedule_time_slots,id',
            ]);


            $tournamentId = $request->get('tournament_id');
            $locationId = $request->get('location_id');
            $beginDate = $request->get('begin_date');
            $timeZone = $request->header('X-Time-Zone') ?? 'UTC';

            $oldConfig = ScheduleConfig::find($request->get('config_id'));

            $conflictId = $this->findConflictConfig($tournamentId, $locationId, $beginDate, $timeZone, $request->get('config_id'));

            if ($conflictId) {
                Log::info('$conflictId', [$conflictId]);

                $conflictConfig = ScheduleConfig::find($conflictId);

                $this->updateOrCreateConfig(
                    $request->get('tournament_id'),
                    $request->get('location_id'),
                    $request->get('begin_date'),
                    $request->get('begin_time'),
                    $request->get('end_time'),
                    $request->get('match_duration'),
                    $request->get('break_duration'),
                    $timeZone,
                    true,
                    $conflictId,
                );
                $oldConfig->delete();

            } else {
                $this->updateOrCreateConfig(
                    $request->get('tournament_id'),
                    $request->get('location_id'),
                    $request->get('begin_date'),
                    $request->get('begin_time'),
                    $request->get('end_time'), // need to find way change
                    $request->get('match_duration'),
                    $request->get('break_duration'),
                    $timeZone,
                    true,
                    $request->get('config_id'),
                );
            }

            $newConfig = ScheduleConfig::find($conflictId ?: $request->get('config_id'));


            $initStartTime = Carbon::parse($newConfig->begin_date->format('Y-m-d') . ' ' . $newConfig->begin_time->format('H:i:s'), 'UTC')->format('Y-m-d H:i:s');
            $initEndTime = Carbon::parse($newConfig->begin_date->format('Y-m-d') . ' ' . $newConfig->begin_time->format('H:i:s'), 'UTC')->addMinutes($newConfig->match_duration)->format('Y-m-d H:i:s');

            ScheduleTimeSlot::whereIn('id', $request->get('time_slot_ids'))
                ->update([
                    'start_time' => $initStartTime,
                    'end_time' => $initEndTime,
                ]);

            $this->timeSlotController->regenerateTimeSlots(
                $request->get('tournament_id'),
                $request->get('location_id'),
                null,
                $conflictId ?: $request->get('config_id'),
                $timeZone,
                min($conflictId ? $conflictConfig->begin_date : $oldConfig->begin_date, $newConfig->begin_date),
                min($conflictId ? $conflictConfig->begin_time : $oldConfig->begin_time, $newConfig->begin_time),
            );

            return response()->json([
                'success' => true,
                'message' => 'Schedule configuration updated successfully.',
            ]);
        } catch (Exception $error) {

            Log::error('Error updating schedule config', [$error]);

            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function updateScheduleStatus($tournamentId)
    {
        try {
            $currentTournament = Tournament::find($tournamentId);

            if (!$currentTournament) {
                return response()->json([
                    'status' => "error",
                    'message' => 'Tournament not found',
                ], 400);
            }

            $currentTournament->is_locked_schedule = !$currentTournament->is_locked_schedule;
            $currentTournament->save();

            return response()->json([
                'status' => "success",
                'message' => 'Update schedule status successfully',
                'data' => $currentTournament->is_locked_schedule,
            ]);
        } catch (Exception $error) {
            Log::error('Error updating schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }

    public function clearUnusedConfigs($tournamentId)
    {
        try {
            // let make elequent query that find all the location in configs that not have any matches or time slot
            ScheduleConfig::where('tournament_id', $tournamentId)
                ->whereDoesntHave('scheduleMatches')
                ->whereDoesntHave('scheduleTimeSlots')
                ->delete();

        } catch (Exception $error) {
            Log::error('Error removing unused schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }

    public function clearAllConfigs($tournamentId)
    {
        try {
            ScheduleConfig::where('tournament_id', $tournamentId)->delete();
        } catch (Exception $error) {
            Log::error('Error removing all schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }
}
